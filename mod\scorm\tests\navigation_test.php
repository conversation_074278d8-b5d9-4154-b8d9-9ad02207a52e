<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Unit tests for SCORM navigation options.
 *
 * @package    mod_scorm
 * @copyright  2024 Sebrae
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot . '/mod/scorm/locallib.php');

/**
 * Test SCORM navigation display options.
 *
 * @package    mod_scorm
 * @copyright  2024 Sebrae
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
class mod_scorm_navigation_test extends advanced_testcase {

    /**
     * Test that the navigation display array includes the new header option.
     */
    public function test_scorm_get_navigation_display_array() {
        $this->resetAfterTest();

        $navigation_options = scorm_get_navigation_display_array();

        // Test that all expected options are present
        $this->assertArrayHasKey(SCORM_NAV_DISABLED, $navigation_options);
        $this->assertArrayHasKey(SCORM_NAV_UNDER_CONTENT, $navigation_options);
        $this->assertArrayHasKey(SCORM_NAV_FLOATING, $navigation_options);
        $this->assertArrayHasKey(SCORM_NAV_HEADER, $navigation_options);

        // Test that the new header option has the correct string
        $this->assertEquals(get_string('header', 'scorm'), $navigation_options[SCORM_NAV_HEADER]);

        // Test that we have exactly 4 options
        $this->assertCount(4, $navigation_options);
    }

    /**
     * Test that the SCORM_NAV_HEADER constant is defined correctly.
     */
    public function test_scorm_nav_header_constant() {
        $this->assertEquals(3, SCORM_NAV_HEADER);
    }

    /**
     * Test creating a SCORM activity with header navigation.
     */
    public function test_scorm_with_header_navigation() {
        global $DB;
        $this->resetAfterTest();

        $course = $this->getDataGenerator()->create_course();
        
        // Create SCORM activity with header navigation
        $scorm = $this->getDataGenerator()->create_module('scorm', [
            'course' => $course->id,
            'nav' => SCORM_NAV_HEADER
        ]);

        // Verify the navigation setting was saved correctly
        $scorm_record = $DB->get_record('scorm', ['id' => $scorm->id]);
        $this->assertEquals(SCORM_NAV_HEADER, $scorm_record->nav);
    }
}
