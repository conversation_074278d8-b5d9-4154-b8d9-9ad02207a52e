# SCORM Navigation Header Implementation

## Descrição
Esta implementação adiciona uma nova opção de navegação para atividades SCORM: **"Fixo no Header"**. Esta opção exibe apenas dois botões de navegação ("Tópico Anterior" e "Próximo Tópico") posicionados de forma fixa no lado esquerdo do topo da página, seguindo o design especificado pelo usuário.

## Características da Implementação

### Botões de Navegação
- **Apenas 2 botões**: "Tópico Anterior" e "Próximo Tópico"
- **Posicionamento**: Lado esquerdo do header (fixo)
- **Textos personalizados**: Substituem os símbolos padrão (< >) por textos descritivos
- **Cores diferenciadas**: 
  - Tópico Anterior: Cinza (#6c757d)
  - Próximo Tópico: Verde (#28a745)

### Design Visual
- **Posição fixa**: No topo da página, lado esquerdo
- **Background semi-transparente**: Com blur effect
- **Botões responsivos**: Com hover effects e estados disabled
- **Design moderno**: Bordas arredondadas e sombras suaves

## Arquivos Modificados

### 1. mod/scorm/lib.php
- Adicionada nova constante: `SCORM_NAV_HEADER = 3`

### 2. mod/scorm/locallib.php
- Atualizada função `scorm_get_navigation_display_array()` para incluir a nova opção

### 3. mod/scorm/lang/en/scorm.php
- Adicionada string: `$string['header'] = 'Fixed in Header';`
- Atualizada descrição de ajuda para incluir a nova opção

### 4. mod/scorm/lang/pt_br/scorm.php
- Adicionada string: `$string['header'] = 'Fixo no Header';`

### 5. mod/scorm/lang/es/scorm.php
- Adicionada string: `$string['header'] = 'Fijo en Encabezado';`

### 6. mod/scorm/module.js
- **HTML personalizado**: Apenas 2 botões com textos "Tópico Anterior" e "Próximo Tópico"
- **Lógica simplificada**: Removidos botões skip, up e outros controles
- **Posicionamento**: Inserção no lado esquerdo do header
- **Função scorm_fixnav**: Atualizada para trabalhar apenas com 2 botões

### 7. mod/scorm/styles.css
- **Posicionamento**: `position: absolute` dentro do header existente
- **Centralização vertical**: `top: 50%` com `transform: translateY(-50%)`
- **Lado esquerdo**: `left: 20px`
- **Integração**: Background transparente para integrar com o header
- **Estilos dos botões**: Cores diferenciadas e textos personalizados
- **Responsividade**: Adaptação para dispositivos móveis
- **Classes específicas**: `.scorm-nav-prev` e `.scorm-nav-next`

## Como Testar

### 1. Criar uma Atividade SCORM
1. Acesse um curso no Moodle
2. Ative o modo de edição
3. Adicione uma atividade SCORM
4. Na seção "Aparência", localize o campo "Mostrar Navegação"
5. Selecione a opção "Fixo no Header"
6. Salve a atividade

### 2. Verificar Funcionamento
1. Acesse a atividade SCORM criada
2. Verifique se apenas 2 botões aparecem no lado esquerdo do header:
   - "Tópico Anterior" (cinza)
   - "Próximo Tópico" (verde)
3. Teste a navegação entre os SCOs
4. Verifique se os botões ficam desabilitados quando apropriado

## Opções de Navegação Disponíveis

1. **Não** (SCORM_NAV_DISABLED = 0): Botões de navegação não são exibidos
2. **Bajo el contenido** (SCORM_NAV_UNDER_CONTENT = 1): Botões exibidos abaixo do conteúdo SCORM
3. **Flotante** (SCORM_NAV_FLOATING = 2): Botões flutuantes com posição configurável
4. **Fixo no Header** (SCORM_NAV_HEADER = 3): Apenas 2 botões fixos no lado esquerdo do header

## Compatibilidade
- Compatível com temas padrão do Moodle
- Funciona com diferentes versões do SCORM (1.2 e 2004)
- Responsivo para dispositivos móveis
- Acessível para usuários com deficiências

## Notas Técnicas
- Não requer alterações no banco de dados (usa campo `nav` existente)
- Mantém compatibilidade com configurações existentes
- Implementação segura sem quebrar funcionalidades existentes
- Removidos botões desnecessários (skip, up) conforme solicitado
